# -*- coding: utf-8 -*-
"""
网络监听模块
使用Chrome DevTools Protocol进行网络监听和数据捕获
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

import websockets
from PyQt6.QtCore import QObject, pyqtSignal, QThread

from utils.logger import setup_logger
from models.network_data import NetworkRequest, NetworkResponse


class NetworkMonitor(QObject):
    """网络监听器"""

    # 信号定义
    request_captured = pyqtSignal(object)  # NetworkRequest
    response_captured = pyqtSignal(object)  # NetworkResponse
    monitoring_started = pyqtSignal()
    monitoring_stopped = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.logger = setup_logger("NetworkMonitor")
        self.websocket = None
        self.is_monitoring = False
        self.request_id_map = {}  # 请求ID映射
        self.captured_requests = []
        self.captured_responses = []

    async def connect_to_devtools(self, websocket_url: str) -> bool:
        """连接到Chrome DevTools"""
        try:
            self.websocket = await websockets.connect(websocket_url)
            self.logger.info(f"已连接到DevTools: {websocket_url}")
            return True
        except Exception as e:
            self.logger.error(f"连接DevTools失败: {e}")
            self.error_occurred.emit(str(e))
            return False

    async def start_monitoring(self) -> bool:
        """开始网络监听"""
        if not self.websocket:
            self.logger.error("未连接到DevTools")
            return False

        try:
            # 启用网络域
            await self._send_command("Network.enable")

            # 启用运行时域
            await self._send_command("Runtime.enable")

            # 设置用户代理覆盖
            await self._send_command("Network.setUserAgentOverride", {
                "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })

            # 开始监听消息
            self.is_monitoring = True
            self.monitoring_started.emit()

            # 启动消息监听循环
            await self._listen_for_messages()

            return True

        except Exception as e:
            self.logger.error(f"开始监听失败: {e}")
            self.error_occurred.emit(str(e))
            return False

    async def _send_command(self, method: str, params: Dict = None) -> Dict:
        """发送DevTools命令"""
        command = {
            "id": int(time.time() * 1000),
            "method": method
        }

        if params:
            command["params"] = params

        await self.websocket.send(json.dumps(command))

        # 等待响应
        response = await self.websocket.recv()
        return json.loads(response)

    async def _listen_for_messages(self):
        """监听DevTools消息"""
        try:
            async for message in self.websocket:
                if not self.is_monitoring:
                    break

                try:
                    data = json.loads(message)
                    await self._handle_devtools_message(data)
                except json.JSONDecodeError:
                    continue

        except websockets.exceptions.ConnectionClosed:
            self.logger.info("DevTools连接已关闭")
        except Exception as e:
            self.logger.error(f"消息监听错误: {e}")
            self.error_occurred.emit(str(e))

    async def _handle_devtools_message(self, data: Dict):
        """处理DevTools消息"""
        method = data.get("method", "")
        params = data.get("params", {})

        if method == "Network.requestWillBeSent":
            await self._handle_request_will_be_sent(params)
        elif method == "Network.responseReceived":
            await self._handle_response_received(params)
        elif method == "Network.loadingFinished":
            await self._handle_loading_finished(params)
        elif method == "Network.loadingFailed":
            await self._handle_loading_failed(params)

    async def _handle_request_will_be_sent(self, params: Dict):
        """处理请求发送事件"""
        request_id = params.get("requestId")
        request_data = params.get("request", {})

        # 创建网络请求对象
        network_request = NetworkRequest(
            request_id=request_id,
            url=request_data.get("url", ""),
            method=request_data.get("method", "GET"),
            headers=request_data.get("headers", {}),
            post_data=request_data.get("postData", ""),
            timestamp=datetime.now()
        )

        # 存储请求
        self.request_id_map[request_id] = network_request
        self.captured_requests.append(network_request)

        # 发出信号
        self.request_captured.emit(network_request)

        self.logger.debug(f"捕获请求: {request_data.get('method')} {request_data.get('url')}")

    async def _handle_response_received(self, params: Dict):
        """处理响应接收事件"""
        request_id = params.get("requestId")
        response_data = params.get("response", {})

        # 获取对应的请求
        network_request = self.request_id_map.get(request_id)
        if not network_request:
            return

        # 创建网络响应对象
        network_response = NetworkResponse(
            request_id=request_id,
            status_code=response_data.get("status", 0),
            status_text=response_data.get("statusText", ""),
            headers=response_data.get("headers", {}),
            mime_type=response_data.get("mimeType", ""),
            url=response_data.get("url", ""),
            timestamp=datetime.now()
        )

        # 获取响应体
        try:
            body_response = await self._send_command("Network.getResponseBody", {
                "requestId": request_id
            })

            if "result" in body_response:
                result = body_response["result"]
                network_response.body = result.get("body", "")
                network_response.base64_encoded = result.get("base64Encoded", False)
        except Exception as e:
            self.logger.debug(f"获取响应体失败: {e}")

        # 存储响应
        self.captured_responses.append(network_response)

        # 关联请求和响应
        network_request.response = network_response

        # 发出信号
        self.response_captured.emit(network_response)

        self.logger.debug(f"捕获响应: {response_data.get('status')} {response_data.get('url')}")

    async def _handle_loading_finished(self, params: Dict):
        """处理加载完成事件"""
        request_id = params.get("requestId")
        network_request = self.request_id_map.get(request_id)

        if network_request:
            network_request.loading_finished = True
            network_request.encoded_data_length = params.get("encodedDataLength", 0)

    async def _handle_loading_failed(self, params: Dict):
        """处理加载失败事件"""
        request_id = params.get("requestId")
        network_request = self.request_id_map.get(request_id)

        if network_request:
            network_request.loading_failed = True
            network_request.error_text = params.get("errorText", "")

    async def stop_monitoring(self):
        """停止网络监听"""
        try:
            self.is_monitoring = False

            if self.websocket:
                # 禁用网络域
                await self._send_command("Network.disable")
                await self._send_command("Runtime.disable")

                # 关闭WebSocket连接
                await self.websocket.close()
                self.websocket = None

            self.monitoring_stopped.emit()
            self.logger.info("网络监听已停止")

        except Exception as e:
            self.logger.error(f"停止监听失败: {e}")

    def get_captured_requests(self) -> List[NetworkRequest]:
        """获取捕获的请求列表"""
        return self.captured_requests.copy()

    def get_captured_responses(self) -> List[NetworkResponse]:
        """获取捕获的响应列表"""
        return self.captured_responses.copy()

    def clear_captured_data(self):
        """清空捕获的数据"""
        self.captured_requests.clear()
        self.captured_responses.clear()
        self.request_id_map.clear()
        self.logger.info("已清空捕获数据")

    def cleanup(self):
        """清理资源"""
        if self.is_monitoring:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.stop_monitoring())
                else:
                    loop.run_until_complete(self.stop_monitoring())
            except Exception as e:
                self.logger.error(f"清理网络监听器失败: {e}")


class NetworkMonitorThread(QThread):
    """网络监听线程"""

    def __init__(self, network_monitor: NetworkMonitor):
        super().__init__()
        self.network_monitor = network_monitor
        self.loop = None

    def run(self):
        """运行线程"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        try:
            self.loop.run_forever()
        finally:
            self.loop.close()

    def stop(self):
        """停止线程"""
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)