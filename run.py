#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
用于开发和测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入主程序
from main import main

if __name__ == "__main__":
    print("正在启动专业浏览器抓包工具...")
    print("请确保已安装所有依赖包:")
    print("pip install -r requirements.txt")
    print("playwright install")
    print("-" * 50)

    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)