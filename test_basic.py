#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")

    try:
        from utils.logger import setup_logger
        print("✓ 日志模块导入成功")

        from models.network_data import NetworkRequest, NetworkResponse
        print("✓ 数据模型导入成功")

        from models.data_storage import DataStorage
        print("✓ 数据存储模块导入成功")

        from core.browser_controller import BrowserController
        print("✓ 浏览器控制模块导入成功")

        from core.network_monitor import NetworkMonitor
        print("✓ 网络监听模块导入成功")

        return True

    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_logger():
    """测试日志功能"""
    print("\n测试日志功能...")

    try:
        from utils.logger import setup_logger
        logger = setup_logger("Test")
        logger.info("这是一条测试日志")
        print("✓ 日志功能正常")
        return True
    except Exception as e:
        print(f"✗ 日志测试失败: {e}")
        return False

def test_data_models():
    """测试数据模型"""
    print("\n测试数据模型...")

    try:
        from models.network_data import NetworkRequest, NetworkResponse
        from datetime import datetime

        # 创建测试请求
        request = NetworkRequest(
            request_id="test-123",
            url="https://example.com",
            method="GET",
            headers={"User-Agent": "Test"},
            timestamp=datetime.now()
        )

        # 创建测试响应
        response = NetworkResponse(
            request_id="test-123",
            status_code=200,
            status_text="OK",
            headers={"Content-Type": "text/html"},
            body="<html>Test</html>",
            mime_type="text/html",
            timestamp=datetime.now()
        )

        # 测试方法
        assert request.get_content_type() == ""
        assert response.is_success() == True
        assert response.is_html() == True

        print("✓ 数据模型功能正常")
        return True

    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        return False

def test_data_storage():
    """测试数据存储"""
    print("\n测试数据存储...")

    try:
        from models.data_storage import DataStorage
        from models.network_data import NetworkRequest, NetworkResponse
        from datetime import datetime

        # 创建临时数据库
        storage = DataStorage("test.db")

        # 创建测试数据
        request = NetworkRequest(
            request_id="test-456",
            url="https://test.com",
            method="POST",
            headers={"Content-Type": "application/json"},
            post_data='{"test": true}',
            timestamp=datetime.now()
        )

        response = NetworkResponse(
            request_id="test-456",
            status_code=201,
            status_text="Created",
            headers={"Content-Type": "application/json"},
            body='{"success": true}',
            mime_type="application/json",
            timestamp=datetime.now()
        )

        # 保存数据
        assert storage.save_request(request) == True
        assert storage.save_response(response) == True

        # 查询数据
        requests = storage.get_requests(limit=10)
        assert len(requests) > 0

        retrieved_response = storage.get_response_by_request_id("test-456")
        assert retrieved_response is not None
        assert retrieved_response.status_code == 201

        # 清理
        storage.clear_all_data()
        if os.path.exists("test.db"):
            os.remove("test.db")

        print("✓ 数据存储功能正常")
        return True

    except Exception as e:
        print(f"✗ 数据存储测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("专业浏览器抓包工具 - 基本功能测试")
    print("=" * 50)

    tests = [
        test_imports,
        test_logger,
        test_data_models,
        test_data_storage
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("✓ 所有基本功能测试通过！")
        return 0
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())