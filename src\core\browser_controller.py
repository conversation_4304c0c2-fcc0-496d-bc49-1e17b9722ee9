# -*- coding: utf-8 -*-
"""
浏览器控制模块
使用Playwright控制浏览器，作为默认浏览器启动器
"""

import asyncio
import json
from typing import Optional, Dict, Any, List
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from PyQt6.QtCore import QObject, pyqtSignal, QThread

from utils.logger import setup_logger


class BrowserController(QObject):
    """浏览器控制器"""

    # 信号定义
    browser_started = pyqtSignal()
    browser_stopped = pyqtSignal()
    page_loaded = pyqtSignal(str)  # URL
    navigation_occurred = pyqtSignal(str, str)  # from_url, to_url
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.logger = setup_logger("BrowserController")
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.is_running = False

    async def start_browser(self,
                          browser_type: str = "chromium",
                          headless: bool = False,
                          proxy: Optional[Dict[str, str]] = None) -> bool:
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()

            # 选择浏览器类型
            if browser_type == "chromium":
                browser_launcher = self.playwright.chromium
            elif browser_type == "firefox":
                browser_launcher = self.playwright.firefox
            elif browser_type == "webkit":
                browser_launcher = self.playwright.webkit
            else:
                raise ValueError(f"不支持的浏览器类型: {browser_type}")

            # 浏览器启动选项
            launch_options = {
                "headless": headless,
                "args": [
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--no-default-browser-check"
                ]
            }

            if proxy:
                launch_options["proxy"] = proxy

            # 启动浏览器
            self.browser = await browser_launcher.launch(**launch_options)

            # 创建浏览器上下文
            context_options = {
                "viewport": {"width": 1920, "height": 1080},
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }

            self.context = await self.browser.new_context(**context_options)

            # 创建页面
            self.page = await self.context.new_page()

            # 设置页面事件监听
            await self._setup_page_listeners()

            self.is_running = True
            self.browser_started.emit()
            self.logger.info(f"浏览器启动成功: {browser_type}")

            return True

        except Exception as e:
            self.logger.error(f"浏览器启动失败: {e}")
            self.error_occurred.emit(str(e))
            return False

    async def _setup_page_listeners(self):
        """设置页面事件监听器"""
        if not self.page:
            return

        # 页面加载完成
        self.page.on("load", lambda: self.page_loaded.emit(self.page.url))

        # 页面导航
        self.page.on("framenavigated", self._on_navigation)

        # 控制台消息
        self.page.on("console", self._on_console_message)

        # 页面错误
        self.page.on("pageerror", self._on_page_error)

    def _on_navigation(self, frame):
        """处理页面导航事件"""
        if frame == self.page.main_frame:
            self.navigation_occurred.emit("", frame.url)

    def _on_console_message(self, msg):
        """处理控制台消息"""
        self.logger.debug(f"Console [{msg.type}]: {msg.text}")

    def _on_page_error(self, error):
        """处理页面错误"""
        self.logger.error(f"Page error: {error}")
        self.error_occurred.emit(str(error))

    async def navigate_to(self, url: str) -> bool:
        """导航到指定URL"""
        if not self.page:
            self.logger.error("浏览器未启动")
            return False

        try:
            await self.page.goto(url, wait_until="networkidle")
            self.logger.info(f"导航到: {url}")
            return True
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            self.error_occurred.emit(str(e))
            return False

    async def execute_script(self, script: str) -> Any:
        """执行JavaScript脚本"""
        if not self.page:
            return None

        try:
            result = await self.page.evaluate(script)
            return result
        except Exception as e:
            self.logger.error(f"脚本执行失败: {e}")
            return None

    async def get_cookies(self) -> List[Dict]:
        """获取当前页面的cookies"""
        if not self.context:
            return []

        try:
            cookies = await self.context.cookies()
            return cookies
        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            return []

    async def set_cookies(self, cookies: List[Dict]):
        """设置cookies"""
        if not self.context:
            return

        try:
            await self.context.add_cookies(cookies)
            self.logger.info("Cookies设置成功")
        except Exception as e:
            self.logger.error(f"设置cookies失败: {e}")

    async def take_screenshot(self, path: str = None) -> bytes:
        """截图"""
        if not self.page:
            return None

        try:
            screenshot = await self.page.screenshot(
                path=path,
                full_page=True,
                type="png"
            )
            return screenshot
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None

    async def stop_browser(self):
        """停止浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None

            if self.context:
                await self.context.close()
                self.context = None

            if self.browser:
                await self.browser.close()
                self.browser = None

            if self.playwright:
                await self.playwright.stop()
                self.playwright = None

            self.is_running = False
            self.browser_stopped.emit()
            self.logger.info("浏览器已停止")

        except Exception as e:
            self.logger.error(f"停止浏览器失败: {e}")

    def cleanup(self):
        """清理资源"""
        if self.is_running:
            # 在事件循环中运行异步清理
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，创建任务
                    loop.create_task(self.stop_browser())
                else:
                    # 如果事件循环未运行，直接运行
                    loop.run_until_complete(self.stop_browser())
            except Exception as e:
                self.logger.error(f"清理资源失败: {e}")


class BrowserThread(QThread):
    """浏览器线程"""

    def __init__(self, browser_controller: BrowserController):
        super().__init__()
        self.browser_controller = browser_controller
        self.loop = None

    def run(self):
        """运行线程"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        try:
            self.loop.run_forever()
        finally:
            self.loop.close()

    def stop(self):
        """停止线程"""
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)