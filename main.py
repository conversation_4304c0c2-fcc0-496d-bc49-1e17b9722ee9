#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业浏览器抓包工具
Professional Browser Packet Capture Tool

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QThread
from PyQt6.QtGui import QIcon

from gui.main_window import MainWindow
from core.browser_controller import BrowserController
from core.network_monitor import NetworkMonitor
from utils.logger import setup_logger


class PacketCaptureApp:
    """主应用程序类"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.browser_controller = None
        self.network_monitor = None
        self.logger = setup_logger()

    def initialize(self):
        """初始化应用程序"""
        # 创建QApplication
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("专业浏览器抓包工具")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("PacketCapture")

        # 设置应用图标
        icon_path = Path(__file__).parent / "assets" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))

        # 设置高DPI支持
        self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)

        # 初始化核心组件
        self.browser_controller = BrowserController()
        self.network_monitor = NetworkMonitor()

        # 创建主窗口
        self.main_window = MainWindow(
            browser_controller=self.browser_controller,
            network_monitor=self.network_monitor
        )

        self.logger.info("应用程序初始化完成")

    def run(self):
        """运行应用程序"""
        try:
            self.initialize()
            self.main_window.show()
            return self.app.exec()
        except Exception as e:
            self.logger.error(f"应用程序运行错误: {e}")
            return 1
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        if self.browser_controller:
            self.browser_controller.cleanup()
        if self.network_monitor:
            self.network_monitor.cleanup()
        self.logger.info("应用程序清理完成")


def main():
    """主函数"""
    app = PacketCaptureApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())