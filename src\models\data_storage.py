# -*- coding: utf-8 -*-
"""
数据存储模块
使用SQLite数据库存储抓包数据
"""

import sqlite3
import json
from typing import List, Optional, Dict, Any
from pathlib import Path
from datetime import datetime

from .network_data import NetworkRequest, NetworkResponse
from utils.logger import setup_logger


class DataStorage:
    """数据存储管理器"""

    def __init__(self, db_path: str = "packet_capture.db"):
        self.db_path = Path(db_path)
        self.logger = setup_logger("DataStorage")
        self._init_database()

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建请求表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        request_id TEXT UNIQUE NOT NULL,
                        url TEXT NOT NULL,
                        method TEXT NOT NULL,
                        headers TEXT,
                        post_data TEXT,
                        timestamp TEXT NOT NULL,
                        loading_finished BOOLEAN DEFAULT FALSE,
                        loading_failed BOOLEAN DEFAULT FALSE,
                        error_text TEXT,
                        encoded_data_length INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建响应表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS responses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        request_id TEXT NOT NULL,
                        status_code INTEGER NOT NULL,
                        status_text TEXT,
                        headers TEXT,
                        body TEXT,
                        mime_type TEXT,
                        url TEXT,
                        timestamp TEXT NOT NULL,
                        base64_encoded BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (request_id) REFERENCES requests (request_id)
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_requests_timestamp ON requests (timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_requests_url ON requests (url)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_requests_method ON requests (method)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_responses_request_id ON responses (request_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_responses_status_code ON responses (status_code)')

                conn.commit()
                self.logger.info(f"数据库初始化完成: {self.db_path}")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise

    def save_request(self, request: NetworkRequest) -> bool:
        """保存请求数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO requests
                    (request_id, url, method, headers, post_data, timestamp,
                     loading_finished, loading_failed, error_text, encoded_data_length)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    request.request_id,
                    request.url,
                    request.method,
                    json.dumps(request.headers),
                    request.post_data,
                    request.timestamp.isoformat(),
                    request.loading_finished,
                    request.loading_failed,
                    request.error_text,
                    request.encoded_data_length
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"保存请求失败: {e}")
            return False

    def save_response(self, response: NetworkResponse) -> bool:
        """保存响应数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO responses
                    (request_id, status_code, status_text, headers, body,
                     mime_type, url, timestamp, base64_encoded)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    response.request_id,
                    response.status_code,
                    response.status_text,
                    json.dumps(response.headers),
                    response.body,
                    response.mime_type,
                    response.url,
                    response.timestamp.isoformat(),
                    response.base64_encoded
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"保存响应失败: {e}")
            return False

    def get_requests(self, limit: int = 1000, offset: int = 0,
                    filters: Optional[Dict[str, Any]] = None) -> List[NetworkRequest]:
        """获取请求列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_clause = "WHERE 1=1"
                params = []

                if filters:
                    if "method" in filters:
                        where_clause += " AND method = ?"
                        params.append(filters["method"])

                    if "url_contains" in filters:
                        where_clause += " AND url LIKE ?"
                        params.append(f"%{filters['url_contains']}%")

                    if "start_time" in filters:
                        where_clause += " AND timestamp >= ?"
                        params.append(filters["start_time"])

                    if "end_time" in filters:
                        where_clause += " AND timestamp <= ?"
                        params.append(filters["end_time"])

                query = f'''
                    SELECT request_id, url, method, headers, post_data, timestamp,
                           loading_finished, loading_failed, error_text, encoded_data_length
                    FROM requests
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ? OFFSET ?
                '''

                params.extend([limit, offset])
                cursor.execute(query, params)

                requests = []
                for row in cursor.fetchall():
                    request = NetworkRequest(
                        request_id=row[0],
                        url=row[1],
                        method=row[2],
                        headers=json.loads(row[3]) if row[3] else {},
                        post_data=row[4] or "",
                        timestamp=datetime.fromisoformat(row[5]),
                        loading_finished=bool(row[6]),
                        loading_failed=bool(row[7]),
                        error_text=row[8] or "",
                        encoded_data_length=row[9] or 0
                    )
                    requests.append(request)

                return requests

        except Exception as e:
            self.logger.error(f"获取请求列表失败: {e}")
            return []

    def get_response_by_request_id(self, request_id: str) -> Optional[NetworkResponse]:
        """根据请求ID获取响应"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT request_id, status_code, status_text, headers, body,
                           mime_type, url, timestamp, base64_encoded
                    FROM responses
                    WHERE request_id = ?
                ''', (request_id,))

                row = cursor.fetchone()
                if row:
                    return NetworkResponse(
                        request_id=row[0],
                        status_code=row[1],
                        status_text=row[2] or "",
                        headers=json.loads(row[3]) if row[3] else {},
                        body=row[4] or "",
                        mime_type=row[5] or "",
                        url=row[6] or "",
                        timestamp=datetime.fromisoformat(row[7]),
                        base64_encoded=bool(row[8])
                    )

                return None

        except Exception as e:
            self.logger.error(f"获取响应失败: {e}")
            return None

    def get_request_response_pairs(self, limit: int = 1000, offset: int = 0,
                                  filters: Optional[Dict[str, Any]] = None) -> List[tuple]:
        """获取请求-响应对"""
        requests = self.get_requests(limit, offset, filters)
        pairs = []

        for request in requests:
            response = self.get_response_by_request_id(request.request_id)
            pairs.append((request, response))

        return pairs

    def delete_old_data(self, days: int = 30) -> bool:
        """删除旧数据"""
        try:
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除旧响应
                cursor.execute('DELETE FROM responses WHERE created_at < ?', (cutoff_date,))

                # 删除旧请求
                cursor.execute('DELETE FROM requests WHERE created_at < ?', (cutoff_date,))

                conn.commit()
                self.logger.info(f"已删除{days}天前的数据")
                return True

        except Exception as e:
            self.logger.error(f"删除旧数据失败: {e}")
            return False

    def clear_all_data(self) -> bool:
        """清空所有数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('DELETE FROM responses')
                cursor.execute('DELETE FROM requests')

                conn.commit()
                self.logger.info("已清空所有数据")
                return True

        except Exception as e:
            self.logger.error(f"清空数据失败: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总请求数
                cursor.execute('SELECT COUNT(*) FROM requests')
                total_requests = cursor.fetchone()[0]

                # 总响应数
                cursor.execute('SELECT COUNT(*) FROM responses')
                total_responses = cursor.fetchone()[0]

                # 按方法统计
                cursor.execute('''
                    SELECT method, COUNT(*)
                    FROM requests
                    GROUP BY method
                    ORDER BY COUNT(*) DESC
                ''')
                methods_stats = dict(cursor.fetchall())

                # 按状态码统计
                cursor.execute('''
                    SELECT status_code, COUNT(*)
                    FROM responses
                    GROUP BY status_code
                    ORDER BY COUNT(*) DESC
                ''')
                status_stats = dict(cursor.fetchall())

                return {
                    "total_requests": total_requests,
                    "total_responses": total_responses,
                    "methods": methods_stats,
                    "status_codes": status_stats
                }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}