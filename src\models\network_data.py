# -*- coding: utf-8 -*-
"""
网络数据模型
定义请求和响应的数据结构
"""

import json
import base64
from typing import Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field


@dataclass
class NetworkRequest:
    """网络请求数据模型"""
    request_id: str
    url: str
    method: str
    headers: Dict[str, str] = field(default_factory=dict)
    post_data: str = ""
    timestamp: datetime = field(default_factory=datetime.now)

    # 额外属性
    response: Optional['NetworkResponse'] = None
    loading_finished: bool = False
    loading_failed: bool = False
    error_text: str = ""
    encoded_data_length: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "request_id": self.request_id,
            "url": self.url,
            "method": self.method,
            "headers": self.headers,
            "post_data": self.post_data,
            "timestamp": self.timestamp.isoformat(),
            "loading_finished": self.loading_finished,
            "loading_failed": self.loading_failed,
            "error_text": self.error_text,
            "encoded_data_length": self.encoded_data_length
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NetworkRequest':
        """从字典创建对象"""
        timestamp = datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat()))

        return cls(
            request_id=data.get("request_id", ""),
            url=data.get("url", ""),
            method=data.get("method", "GET"),
            headers=data.get("headers", {}),
            post_data=data.get("post_data", ""),
            timestamp=timestamp,
            loading_finished=data.get("loading_finished", False),
            loading_failed=data.get("loading_failed", False),
            error_text=data.get("error_text", ""),
            encoded_data_length=data.get("encoded_data_length", 0)
        )

    def get_content_type(self) -> str:
        """获取内容类型"""
        return self.headers.get("content-type", "").split(";")[0].strip()

    def get_size(self) -> int:
        """获取请求大小"""
        return len(self.post_data.encode('utf-8')) if self.post_data else 0

    def is_json(self) -> bool:
        """判断是否为JSON请求"""
        content_type = self.get_content_type().lower()
        return "application/json" in content_type

    def is_form_data(self) -> bool:
        """判断是否为表单数据"""
        content_type = self.get_content_type().lower()
        return "application/x-www-form-urlencoded" in content_type or "multipart/form-data" in content_type


@dataclass
class NetworkResponse:
    """网络响应数据模型"""
    request_id: str
    status_code: int
    status_text: str
    headers: Dict[str, str] = field(default_factory=dict)
    body: str = ""
    mime_type: str = ""
    url: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    base64_encoded: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "request_id": self.request_id,
            "status_code": self.status_code,
            "status_text": self.status_text,
            "headers": self.headers,
            "body": self.body,
            "mime_type": self.mime_type,
            "url": self.url,
            "timestamp": self.timestamp.isoformat(),
            "base64_encoded": self.base64_encoded
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NetworkResponse':
        """从字典创建对象"""
        timestamp = datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat()))

        return cls(
            request_id=data.get("request_id", ""),
            status_code=data.get("status_code", 0),
            status_text=data.get("status_text", ""),
            headers=data.get("headers", {}),
            body=data.get("body", ""),
            mime_type=data.get("mime_type", ""),
            url=data.get("url", ""),
            timestamp=timestamp,
            base64_encoded=data.get("base64_encoded", False)
        )

    def get_decoded_body(self) -> str:
        """获取解码后的响应体"""
        if self.base64_encoded and self.body:
            try:
                return base64.b64decode(self.body).decode('utf-8', errors='ignore')
            except Exception:
                return self.body
        return self.body

    def get_size(self) -> int:
        """获取响应大小"""
        body = self.get_decoded_body()
        return len(body.encode('utf-8')) if body else 0

    def is_json(self) -> bool:
        """判断是否为JSON响应"""
        return "application/json" in self.mime_type.lower()

    def is_html(self) -> bool:
        """判断是否为HTML响应"""
        return "text/html" in self.mime_type.lower()

    def is_image(self) -> bool:
        """判断是否为图片响应"""
        return self.mime_type.lower().startswith("image/")

    def is_success(self) -> bool:
        """判断是否为成功响应"""
        return 200 <= self.status_code < 300

    def get_json_data(self) -> Optional[Dict]:
        """获取JSON数据"""
        if self.is_json():
            try:
                body = self.get_decoded_body()
                return json.loads(body)
            except json.JSONDecodeError:
                pass
        return None