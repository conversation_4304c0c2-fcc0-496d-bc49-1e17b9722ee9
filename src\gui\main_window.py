# -*- coding: utf-8 -*-
"""
主窗口界面
专业浏览器抓包工具的主界面
"""

import asyncio
from typing import Optional

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QLineEdit,
    QComboBox, QTableWidget, QTableWidgetItem, QTextEdit, QTabWidget,
    QGroupBox, QCheckBox, QSpinBox, QProgressBar, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

from core.browser_controller import BrowserController, BrowserThread
from core.network_monitor import NetworkMonitor, NetworkMonitorThread
from models.data_storage import DataStorage
from models.network_data import NetworkRequest, NetworkResponse
from utils.logger import setup_logger


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self, browser_controller: BrowserController,
                 network_monitor: NetworkMonitor):
        super().__init__()

        self.browser_controller = browser_controller
        self.network_monitor = network_monitor
        self.data_storage = DataStorage()
        self.logger = setup_logger("MainWindow")

        # 线程管理
        self.browser_thread = None
        self.network_thread = None

        # 界面组件
        self.central_widget = None
        self.toolbar = None
        self.status_bar = None
        self.request_table = None
        self.detail_tabs = None
        self.filter_panel = None

        # 状态
        self.is_capturing = False
        self.captured_requests = []

        self._init_ui()
        self._setup_connections()
        self._setup_threads()

    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("专业浏览器抓包工具 v1.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口图标
        self.setWindowIcon(QIcon("assets/icons/app_icon.png"))

        # 创建菜单栏
        self._create_menu_bar()

        # 创建工具栏
        self._create_toolbar()

        # 创建中央部件
        self._create_central_widget()

        # 创建状态栏
        self._create_status_bar()

        # 应用样式
        self._apply_styles()

    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        new_session_action = QAction("新建会话(&N)", self)
        new_session_action.setShortcut("Ctrl+N")
        new_session_action.triggered.connect(self._new_session)
        file_menu.addAction(new_session_action)

        open_action = QAction("打开(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self._open_file)
        file_menu.addAction(open_action)

        save_action = QAction("保存(&S)", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self._save_file)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 捕获菜单
        capture_menu = menubar.addMenu("捕获(&C)")

        start_capture_action = QAction("开始捕获(&S)", self)
        start_capture_action.setShortcut("F5")
        start_capture_action.triggered.connect(self._start_capture)
        capture_menu.addAction(start_capture_action)

        stop_capture_action = QAction("停止捕获(&T)", self)
        stop_capture_action.setShortcut("F6")
        stop_capture_action.triggered.connect(self._stop_capture)
        capture_menu.addAction(stop_capture_action)

        clear_action = QAction("清空数据(&C)", self)
        clear_action.setShortcut("Ctrl+L")
        clear_action.triggered.connect(self._clear_data)
        capture_menu.addAction(clear_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        browser_settings_action = QAction("浏览器设置(&B)", self)
        browser_settings_action.triggered.connect(self._show_browser_settings)
        tools_menu.addAction(browser_settings_action)

        proxy_settings_action = QAction("代理设置(&P)", self)
        proxy_settings_action.triggered.connect(self._show_proxy_settings)
        tools_menu.addAction(proxy_settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = self.addToolBar("主工具栏")
        self.toolbar.setMovable(False)

        # 开始/停止捕获按钮
        self.start_btn = QPushButton("开始捕获")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.start_btn.clicked.connect(self._start_capture)
        self.toolbar.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止捕获")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)
        self.stop_btn.clicked.connect(self._stop_capture)
        self.stop_btn.setEnabled(False)
        self.toolbar.addWidget(self.stop_btn)

        self.toolbar.addSeparator()

        # URL输入框
        self.toolbar.addWidget(QLabel("URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("输入要访问的URL...")
        self.url_input.setMinimumWidth(300)
        self.url_input.returnPressed.connect(self._navigate_to_url)
        self.toolbar.addWidget(self.url_input)

        # 导航按钮
        self.navigate_btn = QPushButton("访问")
        self.navigate_btn.clicked.connect(self._navigate_to_url)
        self.toolbar.addWidget(self.navigate_btn)

        self.toolbar.addSeparator()

        # 清空按钮
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self._clear_data)
        self.toolbar.addWidget(self.clear_btn)

        # 导出按钮
        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self._export_data)
        self.toolbar.addWidget(self.export_btn)

    def _create_central_widget(self):
        """创建中央部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建过滤面板
        self._create_filter_panel()
        main_layout.addWidget(self.filter_panel)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧：请求列表
        self._create_request_table()
        splitter.addWidget(self.request_table)

        # 右侧：详情面板
        self._create_detail_panel()
        splitter.addWidget(self.detail_tabs)

        # 设置分割器比例
        splitter.setSizes([600, 800])

    def _create_filter_panel(self):
        """创建过滤面板"""
        self.filter_panel = QGroupBox("过滤器")
        filter_layout = QHBoxLayout(self.filter_panel)

        # 方法过滤
        filter_layout.addWidget(QLabel("方法:"))
        self.method_filter = QComboBox()
        self.method_filter.addItems(["全部", "GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"])
        self.method_filter.currentTextChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.method_filter)

        # URL过滤
        filter_layout.addWidget(QLabel("URL包含:"))
        self.url_filter = QLineEdit()
        self.url_filter.setPlaceholderText("输入URL关键词...")
        self.url_filter.textChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.url_filter)

        # 状态码过滤
        filter_layout.addWidget(QLabel("状态码:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "2xx", "3xx", "4xx", "5xx"])
        self.status_filter.currentTextChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.status_filter)

        # 内容类型过滤
        filter_layout.addWidget(QLabel("类型:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["全部", "HTML", "JSON", "XML", "图片", "CSS", "JS"])
        self.type_filter.currentTextChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.type_filter)

        # 只显示错误
        self.error_only_cb = QCheckBox("仅显示错误")
        self.error_only_cb.stateChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.error_only_cb)

        filter_layout.addStretch()

    def _create_request_table(self):
        """创建请求列表表格"""
        self.request_table = QTableWidget()
        self.request_table.setColumnCount(6)
        self.request_table.setHorizontalHeaderLabels([
            "方法", "URL", "状态", "类型", "大小", "时间"
        ])

        # 设置表格属性
        self.request_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.request_table.setAlternatingRowColors(True)
        self.request_table.setSortingEnabled(True)

        # 设置列宽
        header = self.request_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.request_table.setColumnWidth(0, 80)   # 方法
        self.request_table.setColumnWidth(1, 300)  # URL
        self.request_table.setColumnWidth(2, 80)   # 状态
        self.request_table.setColumnWidth(3, 100)  # 类型
        self.request_table.setColumnWidth(4, 80)   # 大小

        # 连接选择事件
        self.request_table.itemSelectionChanged.connect(self._on_request_selected)

    def _create_detail_panel(self):
        """创建详情面板"""
        self.detail_tabs = QTabWidget()

        # 请求详情标签页
        self.request_detail_tab = QWidget()
        self.detail_tabs.addTab(self.request_detail_tab, "请求详情")

        request_layout = QVBoxLayout(self.request_detail_tab)

        # 请求基本信息
        request_info_group = QGroupBox("基本信息")
        request_info_layout = QVBoxLayout(request_info_group)

        self.request_info_text = QTextEdit()
        self.request_info_text.setMaximumHeight(150)
        self.request_info_text.setReadOnly(True)
        request_info_layout.addWidget(self.request_info_text)

        request_layout.addWidget(request_info_group)

        # 请求头
        request_headers_group = QGroupBox("请求头")
        request_headers_layout = QVBoxLayout(request_headers_group)

        self.request_headers_text = QTextEdit()
        self.request_headers_text.setReadOnly(True)
        request_headers_layout.addWidget(self.request_headers_text)

        request_layout.addWidget(request_headers_group)

        # 请求体
        request_body_group = QGroupBox("请求体")
        request_body_layout = QVBoxLayout(request_body_group)

        self.request_body_text = QTextEdit()
        self.request_body_text.setReadOnly(True)
        request_body_layout.addWidget(self.request_body_text)

        request_layout.addWidget(request_body_group)

        # 响应详情标签页
        self.response_detail_tab = QWidget()
        self.detail_tabs.addTab(self.response_detail_tab, "响应详情")

        response_layout = QVBoxLayout(self.response_detail_tab)

        # 响应基本信息
        response_info_group = QGroupBox("基本信息")
        response_info_layout = QVBoxLayout(response_info_group)

        self.response_info_text = QTextEdit()
        self.response_info_text.setMaximumHeight(150)
        self.response_info_text.setReadOnly(True)
        response_info_layout.addWidget(self.response_info_text)

        response_layout.addWidget(response_info_group)

        # 响应头
        response_headers_group = QGroupBox("响应头")
        response_headers_layout = QVBoxLayout(response_headers_group)

        self.response_headers_text = QTextEdit()
        self.response_headers_text.setReadOnly(True)
        response_headers_layout.addWidget(self.response_headers_text)

        response_layout.addWidget(response_headers_group)

        # 响应体
        response_body_group = QGroupBox("响应体")
        response_body_layout = QVBoxLayout(response_body_group)

        self.response_body_text = QTextEdit()
        self.response_body_text.setReadOnly(True)
        response_body_layout.addWidget(self.response_body_text)

        response_layout.addWidget(response_body_group)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 请求计数
        self.request_count_label = QLabel("请求: 0")
        self.status_bar.addPermanentWidget(self.request_count_label)

        # 捕获状态
        self.capture_status_label = QLabel("未捕获")
        self.status_bar.addPermanentWidget(self.capture_status_label)

    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }

            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f8f8;
            }

            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }

            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
            }

            QLineEdit {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                background-color: white;
            }

            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px;
                background-color: white;
            }

            QPushButton {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 5px 10px;
                background-color: white;
            }

            QPushButton:hover {
                background-color: #e6e6e6;
            }

            QPushButton:pressed {
                background-color: #d4d4d4;
            }
        """)

    def _setup_connections(self):
        """设置信号连接"""
        # 浏览器控制器信号
        self.browser_controller.browser_started.connect(self._on_browser_started)
        self.browser_controller.browser_stopped.connect(self._on_browser_stopped)
        self.browser_controller.page_loaded.connect(self._on_page_loaded)
        self.browser_controller.error_occurred.connect(self._on_browser_error)

        # 网络监听器信号
        self.network_monitor.request_captured.connect(self._on_request_captured)
        self.network_monitor.response_captured.connect(self._on_response_captured)
        self.network_monitor.monitoring_started.connect(self._on_monitoring_started)
        self.network_monitor.monitoring_stopped.connect(self._on_monitoring_stopped)
        self.network_monitor.error_occurred.connect(self._on_network_error)

    def _setup_threads(self):
        """设置线程"""
        # 浏览器线程
        self.browser_thread = BrowserThread(self.browser_controller)
        self.browser_thread.start()

        # 网络监听线程
        self.network_thread = NetworkMonitorThread(self.network_monitor)
        self.network_thread.start()

    # 事件处理方法
    def _start_capture(self):
        """开始捕获"""
        if self.is_capturing:
            return

        try:
            # 启动浏览器
            loop = self.browser_thread.loop
            if loop:
                asyncio.run_coroutine_threadsafe(
                    self.browser_controller.start_browser(), loop
                )

            self.is_capturing = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.capture_status_label.setText("正在捕获")
            self.status_label.setText("正在启动浏览器...")

        except Exception as e:
            self.logger.error(f"启动捕获失败: {e}")
            QMessageBox.critical(self, "错误", f"启动捕获失败: {e}")

    def _stop_capture(self):
        """停止捕获"""
        if not self.is_capturing:
            return

        try:
            # 停止网络监听
            loop = self.network_thread.loop
            if loop:
                asyncio.run_coroutine_threadsafe(
                    self.network_monitor.stop_monitoring(), loop
                )

            # 停止浏览器
            loop = self.browser_thread.loop
            if loop:
                asyncio.run_coroutine_threadsafe(
                    self.browser_controller.stop_browser(), loop
                )

            self.is_capturing = False
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.capture_status_label.setText("已停止")
            self.status_label.setText("捕获已停止")

        except Exception as e:
            self.logger.error(f"停止捕获失败: {e}")
            QMessageBox.critical(self, "错误", f"停止捕获失败: {e}")

    def _navigate_to_url(self):
        """导航到URL"""
        url = self.url_input.text().strip()
        if not url:
            return

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            loop = self.browser_thread.loop
            if loop:
                asyncio.run_coroutine_threadsafe(
                    self.browser_controller.navigate_to(url), loop
                )

            self.status_label.setText(f"正在访问: {url}")

        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            QMessageBox.critical(self, "错误", f"导航失败: {e}")

    def _clear_data(self):
        """清空数据"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有捕获的数据吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.captured_requests.clear()
            self.request_table.setRowCount(0)
            self.network_monitor.clear_captured_data()
            self.data_storage.clear_all_data()
            self._update_request_count()
            self.status_label.setText("数据已清空")

    def _export_data(self):
        """导出数据"""
        # TODO: 实现数据导出功能
        QMessageBox.information(self, "提示", "导出功能正在开发中...")

    def _apply_filters(self):
        """应用过滤器"""
        # TODO: 实现过滤功能
        pass

    def _on_request_selected(self):
        """请求选择事件"""
        current_row = self.request_table.currentRow()
        if current_row < 0 or current_row >= len(self.captured_requests):
            return

        request = self.captured_requests[current_row]
        self._display_request_details(request)

        # 获取对应的响应
        response = self.data_storage.get_response_by_request_id(request.request_id)
        if response:
            self._display_response_details(response)

    def _display_request_details(self, request: NetworkRequest):
        """显示请求详情"""
        # 基本信息
        info_text = f"""URL: {request.url}
方法: {request.method}
时间: {request.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
状态: {'加载完成' if request.loading_finished else '加载中' if not request.loading_failed else '加载失败'}
"""
        if request.error_text:
            info_text += f"错误: {request.error_text}\n"

        self.request_info_text.setPlainText(info_text)

        # 请求头
        headers_text = ""
        for key, value in request.headers.items():
            headers_text += f"{key}: {value}\n"
        self.request_headers_text.setPlainText(headers_text)

        # 请求体
        self.request_body_text.setPlainText(request.post_data)

    def _display_response_details(self, response: NetworkResponse):
        """显示响应详情"""
        # 基本信息
        info_text = f"""状态码: {response.status_code} {response.status_text}
MIME类型: {response.mime_type}
大小: {response.get_size()} 字节
时间: {response.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
"""
        self.response_info_text.setPlainText(info_text)

        # 响应头
        headers_text = ""
        for key, value in response.headers.items():
            headers_text += f"{key}: {value}\n"
        self.response_headers_text.setPlainText(headers_text)

        # 响应体
        body = response.get_decoded_body()
        if response.is_json():
            try:
                import json
                formatted_body = json.dumps(json.loads(body), indent=2, ensure_ascii=False)
                self.response_body_text.setPlainText(formatted_body)
            except:
                self.response_body_text.setPlainText(body)
        else:
            self.response_body_text.setPlainText(body)

    # 信号处理方法
    def _on_browser_started(self):
        """浏览器启动完成"""
        self.status_label.setText("浏览器已启动")

        # 启动网络监听
        # TODO: 获取浏览器的DevTools WebSocket URL并连接

    def _on_browser_stopped(self):
        """浏览器停止"""
        self.status_label.setText("浏览器已停止")

    def _on_page_loaded(self, url: str):
        """页面加载完成"""
        self.status_label.setText(f"页面已加载: {url}")
        self.url_input.setText(url)

    def _on_browser_error(self, error: str):
        """浏览器错误"""
        self.status_label.setText(f"浏览器错误: {error}")
        QMessageBox.warning(self, "浏览器错误", error)

    def _on_monitoring_started(self):
        """网络监听开始"""
        self.status_label.setText("网络监听已开始")

    def _on_monitoring_stopped(self):
        """网络监听停止"""
        self.status_label.setText("网络监听已停止")

    def _on_network_error(self, error: str):
        """网络监听错误"""
        self.status_label.setText(f"网络错误: {error}")
        QMessageBox.warning(self, "网络错误", error)

    def _on_request_captured(self, request: NetworkRequest):
        """请求捕获事件"""
        self.captured_requests.append(request)
        self._add_request_to_table(request)
        self._update_request_count()

        # 保存到数据库
        self.data_storage.save_request(request)

    def _on_response_captured(self, response: NetworkResponse):
        """响应捕获事件"""
        # 保存到数据库
        self.data_storage.save_response(response)

        # 更新表格中对应的行
        self._update_request_in_table(response)

    def _add_request_to_table(self, request: NetworkRequest):
        """添加请求到表格"""
        row = self.request_table.rowCount()
        self.request_table.insertRow(row)

        # 方法
        self.request_table.setItem(row, 0, QTableWidgetItem(request.method))

        # URL
        url_item = QTableWidgetItem(request.url)
        url_item.setToolTip(request.url)
        self.request_table.setItem(row, 1, url_item)

        # 状态
        status = "等待响应"
        if request.loading_failed:
            status = "失败"
        elif request.loading_finished:
            status = "完成"
        self.request_table.setItem(row, 2, QTableWidgetItem(status))

        # 类型
        content_type = request.get_content_type()
        self.request_table.setItem(row, 3, QTableWidgetItem(content_type))

        # 大小
        size = request.get_size()
        size_text = f"{size} B" if size < 1024 else f"{size/1024:.1f} KB"
        self.request_table.setItem(row, 4, QTableWidgetItem(size_text))

        # 时间
        time_text = request.timestamp.strftime('%H:%M:%S')
        self.request_table.setItem(row, 5, QTableWidgetItem(time_text))

        # 滚动到最新行
        self.request_table.scrollToBottom()

    def _update_request_in_table(self, response: NetworkResponse):
        """更新表格中的请求状态"""
        # 查找对应的请求行
        for row in range(self.request_table.rowCount()):
            if row < len(self.captured_requests):
                request = self.captured_requests[row]
                if request.request_id == response.request_id:
                    # 更新状态
                    status_text = f"{response.status_code}"
                    if response.is_success():
                        status_text += " ✓"
                    else:
                        status_text += " ✗"

                    self.request_table.setItem(row, 2, QTableWidgetItem(status_text))

                    # 更新类型
                    self.request_table.setItem(row, 3, QTableWidgetItem(response.mime_type))

                    # 更新大小
                    size = response.get_size()
                    size_text = f"{size} B" if size < 1024 else f"{size/1024:.1f} KB"
                    self.request_table.setItem(row, 4, QTableWidgetItem(size_text))
                    break

    def _update_request_count(self):
        """更新请求计数"""
        count = len(self.captured_requests)
        self.request_count_label.setText(f"请求: {count}")

    # 菜单事件处理
    def _new_session(self):
        """新建会话"""
        self._clear_data()

    def _open_file(self):
        """打开文件"""
        # TODO: 实现文件打开功能
        QMessageBox.information(self, "提示", "文件打开功能正在开发中...")

    def _save_file(self):
        """保存文件"""
        # TODO: 实现文件保存功能
        QMessageBox.information(self, "提示", "文件保存功能正在开发中...")

    def _show_browser_settings(self):
        """显示浏览器设置"""
        # TODO: 实现浏览器设置对话框
        QMessageBox.information(self, "提示", "浏览器设置功能正在开发中...")

    def _show_proxy_settings(self):
        """显示代理设置"""
        # TODO: 实现代理设置对话框
        QMessageBox.information(self, "提示", "代理设置功能正在开发中...")

    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         "专业浏览器抓包工具 v1.0\n\n"
                         "一个功能强大的网络抓包分析工具\n"
                         "基于Python + PyQt6 + Playwright开发\n\n"
                         "作者: AI Assistant")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_capturing:
            self._stop_capture()

        # 停止线程
        if self.browser_thread:
            self.browser_thread.stop()
            self.browser_thread.wait()

        if self.network_thread:
            self.network_thread.stop()
            self.network_thread.wait()

        event.accept()