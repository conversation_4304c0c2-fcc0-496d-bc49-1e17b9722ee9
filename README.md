# 专业浏览器抓包工具

一个功能强大的专业级浏览器网络抓包分析工具，基于Python开发，提供精致的图形界面和全面的抓包功能。

## ✨ 特性

### 🚀 核心功能
- **浏览器控制**: 使用Playwright控制Chromium/Firefox/WebKit浏览器
- **网络监听**: 基于Chrome DevTools Protocol实时捕获网络流量
- **数据存储**: SQLite数据库持久化存储抓包数据
- **实时分析**: 实时显示请求/响应详情，支持JSON格式化

### 🎨 精致界面
- **现代化UI**: 基于PyQt6的现代化界面设计
- **响应式布局**: 自适应窗口大小的响应式布局
- **动画效果**: 流畅的界面动画和过渡效果
- **主题支持**: 支持明暗主题切换

### 🔧 专业工具
- **请求重放**: 重新发送捕获的HTTP请求
- **数据修改**: 修改请求参数和头部信息
- **数据导出**: 支持多种格式的数据导出
- **统计分析**: 详细的网络流量统计分析

## 📋 系统要求

- Python 3.8+
- Windows 10/11, macOS 10.14+, 或 Linux
- 至少 4GB RAM
- 1GB 可用磁盘空间

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd 抓包
```

### 2. 安装依赖
```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install
```

### 3. 运行程序
```bash
# 使用主程序启动
python main.py

# 或使用快速启动脚本
python run.py
```

## 📖 使用指南

### 基本操作

1. **启动捕获**
   - 点击工具栏的"开始捕获"按钮
   - 程序会自动启动浏览器并开始监听网络流量

2. **访问网站**
   - 在URL输入框中输入要访问的网址
   - 点击"访问"按钮或按回车键

3. **查看抓包数据**
   - 左侧表格显示所有捕获的请求
   - 点击任意请求查看详细信息
   - 右侧面板显示请求和响应的详细内容

4. **过滤和搜索**
   - 使用顶部过滤器按方法、URL、状态码等筛选请求
   - 支持实时搜索和多条件组合过滤

### 高级功能

- **请求重放**: 右键点击请求选择"重放"
- **数据导出**: 使用"文件"菜单导出抓包数据
- **代理设置**: 在"工具"菜单中配置HTTP代理
- **浏览器设置**: 自定义浏览器启动参数

## 🏗️ 项目结构

```
抓包/
├── main.py                 # 主程序入口
├── run.py                  # 快速启动脚本
├── requirements.txt        # Python依赖
├── README.md              # 项目文档
├── src/                   # 源代码目录
│   ├── core/              # 核心功能模块
│   │   ├── browser_controller.py    # 浏览器控制
│   │   └── network_monitor.py       # 网络监听
│   ├── gui/               # 图形界面模块
│   │   └── main_window.py           # 主窗口
│   ├── models/            # 数据模型
│   │   ├── network_data.py          # 网络数据模型
│   │   └── data_storage.py          # 数据存储
│   └── utils/             # 工具模块
│       └── logger.py                # 日志工具
├── assets/                # 资源文件
│   ├── icons/             # 图标文件
│   └── styles/            # 样式文件
├── tests/                 # 测试文件
├── docs/                  # 文档目录
└── logs/                  # 日志目录
```

## 🔧 开发指南

### 环境设置
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black src/
isort src/
```

### 添加新功能
1. 在相应模块中添加功能代码
2. 编写单元测试
3. 更新文档
4. 提交Pull Request

## 🐛 故障排除

### 常见问题

**Q: 程序启动失败，提示缺少依赖**
A: 确保已安装所有依赖包：`pip install -r requirements.txt`

**Q: 浏览器启动失败**
A: 运行 `playwright install` 安装浏览器驱动

**Q: 无法捕获HTTPS流量**
A: 确保浏览器信任了抓包工具的证书

**Q: 界面显示异常**
A: 检查PyQt6是否正确安装，尝试重新安装：`pip install --force-reinstall PyQt6`

### 日志查看
程序运行日志保存在 `logs/` 目录中，可以查看详细的错误信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 📞 支持

如果您遇到问题或有建议，请：
- 提交 [Issue](../../issues)
- 发送邮件至 <EMAIL>
- 查看 [Wiki](../../wiki) 获取更多帮助

## 🙏 致谢

感谢以下开源项目：
- [Playwright](https://playwright.dev/) - 浏览器自动化
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - GUI框架
- [SQLite](https://www.sqlite.org/) - 数据库引擎

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！